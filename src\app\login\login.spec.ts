import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';

import { LoginComponent } from './login.component';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LoginComponent],
      providers: [provideRouter([])]
    })
    .compileComponents();

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should block non-numeric keys in student ID input', () => {
    const keyboardEvent = new KeyboardEvent('keydown', { key: 'a' });
    spyOn(keyboardEvent, 'preventDefault');

    component.onStudentIdKeydown(keyboardEvent);

    expect(keyboardEvent.preventDefault).toHaveBeenCalled();
  });

  it('should allow numeric keys in student ID input', () => {
    const keyboardEvent = new KeyboardEvent('keydown', { key: '5' });
    spyOn(keyboardEvent, 'preventDefault');

    component.onStudentIdKeydown(keyboardEvent);

    expect(keyboardEvent.preventDefault).not.toHaveBeenCalled();
  });

  it('should allow navigation keys in student ID input', () => {
    const keyboardEvent = new KeyboardEvent('keydown', { key: 'Backspace' });
    spyOn(keyboardEvent, 'preventDefault');

    component.onStudentIdKeydown(keyboardEvent);

    expect(keyboardEvent.preventDefault).not.toHaveBeenCalled();
  });

  it('should format student ID with dash after 4 digits', () => {
    const input = document.createElement('input');
    input.value = '123456789';

    const event = { target: input } as unknown as Event;
    component.onStudentIdInput(event);

    expect(input.value).toBe('1234-56789');
  });

  it('should prevent paste of non-numeric content', () => {
    const input = document.createElement('input');
    const clipboardEvent = new ClipboardEvent('paste');
    Object.defineProperty(clipboardEvent, 'clipboardData', {
      value: {
        getData: () => 'abc123def456'
      }
    });
    Object.defineProperty(clipboardEvent, 'target', { value: input });

    spyOn(clipboardEvent, 'preventDefault');

    component.onStudentIdPaste(clipboardEvent);

    expect(clipboardEvent.preventDefault).toHaveBeenCalled();
    expect(input.value).toBe('1234-56');
  });
});
