import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [RouterModule],
  templateUrl: './login.html',
  styleUrl: './login.css'
})
export class LoginComponent {

  blockNonNumericKeys(event: KeyboardEvent): void {
    const allowedKeys = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete', 'Home', 'End'];

    // Allow navigation and control keys
    if (allowedKeys.includes(event.key)) {
      return;
    }

    // Only allow digits 0-9
    if (!/^[0-9]$/.test(event.key)) {
      event.preventDefault();
    }
  }

  formatStudentId(event: Event): void {
    const input = event.target as HTMLInputElement;

    // Remove all non-numeric characters
    let value = input.value.replace(/\D/g, '');

    // Limit to maximum 9 digits
    if (value.length > 9) {
      value = value.slice(0, 9);
    }

    // Format: XXXX-XXXXX (4 digits, dash, 5 digits)
    if (value.length > 4) {
      input.value = `${value.slice(0, 4)}-${value.slice(4)}`;
    } else {
      input.value = value;
    }

    // Validate the format
    this.validateStudentId(input);
  }

  validateStudentId(input: HTMLInputElement): void {
    const pattern = /^[0-9]{4}-[0-9]{5}$/;
    const errorDiv = document.getElementById('studentIdError');

    if (input.value && !pattern.test(input.value)) {
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
      errorDiv?.classList.remove('hidden');
    } else {
      input.classList.remove('border-red-500');
      input.classList.add('border-gray-200');
      errorDiv?.classList.add('hidden');
    }
  }

}


